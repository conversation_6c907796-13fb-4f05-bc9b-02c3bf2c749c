// This script will handle the logic for the popup UI.
// - Fetching and displaying conversations
// - Handling search and filter events
// - Showing conversation details

let allConversations = []; // Cache all conversations
let currentView = 'list'; // 'list' or 'detail'

document.addEventListener('DOMContentLoaded', () => {
  loadConversations();

  // Configure marked to use highlight.js for syntax highlighting
  // Configure marked to use highlight.js for syntax highlighting.
  // This is the recommended, robust way to integrate the two libraries.
  // Configure marked for GitHub Flavored Markdown.
  // We will handle highlighting separately after the content is in the DOM.
  marked.setOptions({
    gfm: true,
    breaks: true
  });
  
  const searchInput = document.getElementById('search-input');
  searchInput.addEventListener('input', (e) => {
    const searchTerm = e.target.value.toLowerCase();
    const filteredConversations = allConversations.filter(c => 
      c.title.toLowerCase().includes(searchTerm) ||
      c.prompt.toLowerCase().includes(searchTerm) ||
      c.response.toLowerCase().includes(searchTerm)
    );
    renderConversations(filteredConversations);
  });

  document.getElementById('back-button').addEventListener('click', () => {
    showListView();
  });
});

function loadConversations() {
  chrome.runtime.sendMessage({ namespace: 'database', action: 'getAllConversations' }, (response) => {
    if (chrome.runtime.lastError) {
      console.error("Error loading conversations:", chrome.runtime.lastError);
      return;
    }
    if (response && response.status === 'success') {
      allConversations = response.data;
      renderConversations(allConversations);
    } else {
      console.error("Error loading conversations:", response ? response.message : "No response");
    }
  });
}

function renderConversations(conversations) {
  const listElement = document.getElementById('conversation-list');
  listElement.innerHTML = ''; // Clear existing list

  if (!conversations || conversations.length === 0) {
    listElement.innerHTML = '<p class="empty-message">No conversations recorded yet.</p>';
    return;
  }

  conversations.forEach(conv => {
    const item = document.createElement('div');
    item.className = 'conversation-item';
    item.innerHTML = `
      <div class="item-header">
        <span class="platform-badge ${conv.platform.toLowerCase()}">${conv.platform}</span>
        <span class="item-title">${escapeHTML(conv.title)}</span>
        <span class="item-date">${new Date(conv.createdAt).toLocaleString()}</span>
      </div>
      <div class="item-preview">
        <strong>You:</strong> ${escapeHTML(conv.prompt.substring(0, 100))}...
      </div>
    `;
    item.addEventListener('click', () => {
      showDetailView(conv);
    });

    const menuButton = document.createElement('button');
    menuButton.className = 'menu-button';
    menuButton.innerHTML = '...';
    menuButton.addEventListener('click', (e) => {
      e.stopPropagation();
      // We will implement a custom context menu here in a future step
      console.log("Menu button clicked for:", conv.id);
      if (confirm(`Are you sure you want to delete this conversation?\n\n"${conv.prompt.substring(0, 50)}..."`)) {
        deleteConversation(conv.id);
      }
    });

    item.querySelector('.item-header').appendChild(menuButton);
    listElement.appendChild(item);
  });
}

function showDetailView(conversation) {
  currentView = 'detail';
  document.getElementById('list-view').classList.add('hidden');
  const detailView = document.getElementById('detail-view');
  detailView.classList.remove('hidden');
  
  document.getElementById('detail-title').textContent = conversation.title;
  
  const detailElement = document.getElementById('conversation-detail');
  detailElement.innerHTML = `
    <div class="detail-section">
      <div class="detail-meta">
        <strong>Platform:</strong> ${conversation.platform} | 
        <strong>Date:</strong> ${new Date(conversation.createdAt).toLocaleString()} |
        <strong>URL:</strong> <a href="${conversation.url}" target="_blank">${escapeHTML(conversation.url)}</a>
      </div>
    </div>
    <div class="detail-section">
      <h2>Prompt</h2>
      <div class="detail-content">${escapeHTML(conversation.prompt)}</div>
    </div>
    <div class="detail-section">
      <h2>Response</h2>
      <div class="detail-content">${DOMPurify.sanitize(marked.parse(conversation.response || ''))}</div>
    </div>
  `;

  enhanceCodeBlocks(detailElement);
}

function enhanceCodeBlocks(container) {
  // Find all code blocks within the container.
  const codeElements = container.querySelectorAll('pre code');

  codeElements.forEach(codeEl => {
    // 1. Highlight the code block.
    // highlightElement will automatically detect the language.
    hljs.highlightElement(codeEl);

    // 2. Add a copy button to the parent <pre> element.
    const preEl = codeEl.parentElement;
    if (preEl.querySelector('.copy-button')) return; // Avoid adding duplicate buttons

    const button = document.createElement('button');
    button.className = 'copy-button';
    button.textContent = 'Copy';
    
    button.addEventListener('click', (e) => {
      e.stopPropagation();
      navigator.clipboard.writeText(codeEl.innerText).then(() => {
        button.textContent = 'Copied!';
        setTimeout(() => {
          button.textContent = 'Copy';
        }, 2000);
      }).catch(err => {
        console.error('Failed to copy code: ', err);
        button.textContent = 'Error';
      });
    });

    preEl.appendChild(button);
  });
}

function showListView() {
  currentView = 'list';
  document.getElementById('detail-view').classList.add('hidden');
  document.getElementById('list-view').classList.remove('hidden');
}

function deleteConversation(id) {
  chrome.runtime.sendMessage({ namespace: 'database', action: 'deleteConversation', payload: { id: id } }, (response) => {
    if (response.status === 'success') {
      loadConversations(); // Refresh the list
      if (currentView === 'detail') {
        showListView();
      }
    } else {
      console.error("Error deleting conversation:", response.message);
    }
  });
}

function escapeHTML(str) {
    if (!str) return '';
    const div = document.createElement('div');
    div.textContent = str;
    return div.innerHTML;
}